#Requires -RunAsAdministrator

<#
.SYNOPSIS
    Modular SSL Certificate Renewal Script for cvfnotaires.ca
.DESCRIPTION
    Automates the SSL certificate renewal process for IIS, RD Gateway, RD Web Access, and RD Session Host
    with options to renew public certificate, self-signed certificate, or both
.NOTES
    File Location: C:\SSL\Complete-SSL-Renewal.ps1
    Requires: OpenSSL, Administrator rights
.PARAMETER RenewalType
    Specifies which certificates to renew: 'Public', 'SelfSigned', or 'Both'
.PARAMETER SSLPath
    Path to SSL directory (default: C:\SSL)
.PARAMETER PrivateKeyFile
    Private key filename (default: 3aa5622fb31b138e.key)
.PARAMETER Interactive
    Run in interactive mode to choose renewal type
#>

# Script parameters
param(
    [Parameter(Mandatory = $false)]
    [ValidateSet('Public', 'SelfSigned', 'Both')]
    [string]$RenewalType,
    
    [Parameter(Mandatory = $false)]
    [string]$SSLPath = "C:\SSL",
    
    [Parameter(Mandatory = $false)]
    [string]$PrivateKeyFile = "3aa5622fb31b138e.key",
    
    [Parameter(Mandatory = $false)]
    [switch]$Interactive
)

# Set variables
$CertificatesPath = Join-Path $SSLPath "certificates"
$PrivateKeyPath = Join-Path $SSLPath $PrivateKeyFile
$OutputPFXPath = Join-Path $SSLPath "cvfnotaires-new.pfx"
$Year = Get-Date -Format "yyyy"

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to show banner
function Show-Banner {
    Clear-Host
    Write-ColorOutput "========================================" "Cyan"
    Write-ColorOutput "   SSL Certificate Renewal Automation   " "Cyan"
    Write-ColorOutput "         cvfnotaires.ca                 " "Cyan"
    Write-ColorOutput "========================================" "Cyan"
    Write-ColorOutput "Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" "Gray"
    Write-ColorOutput "User: $env:USERNAME" "Gray"
    Write-Host ""
}

# Function to get renewal type from user
function Get-RenewalType {
    if ($Interactive -or -not $RenewalType) {
        Write-ColorOutput "=== Select Renewal Type ===" "Cyan"
        Write-Host ""
        Write-ColorOutput "1. Public Certificate Only (cvfnotaires.ca)" "White"
        Write-ColorOutput "   - Updates IIS bindings" "Gray"
        Write-ColorOutput "   - For RD Gateway and RD Web Access" "Gray"
        Write-Host ""
        Write-ColorOutput "2. Self-Signed Certificate Only (cvfnotaires.local)" "White"
        Write-ColorOutput "   - For RD Session Host" "Gray"
        Write-Host ""
        Write-ColorOutput "3. Both Certificates" "White"
        Write-ColorOutput "   - Complete renewal of all certificates" "Gray"
        Write-Host ""
        
        do {
            $choice = Read-Host "Enter your choice (1-3)"
            switch ($choice) {
                "1" { return "Public" }
                "2" { return "SelfSigned" }
                "3" { return "Both" }
                default { Write-ColorOutput "Invalid choice. Please enter 1, 2, or 3." "Red" }
            }
        } while ($true)
    }
    
    return $RenewalType
}

# Function to check prerequisites
function Test-Prerequisites {
    param(
        [string]$Type
    )
    
    Write-ColorOutput "=== Checking Prerequisites for $Type renewal ===" "Cyan"
    
    $allGood = $true
    
    # Check if running as administrator
    if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
        Write-ColorOutput "✗ ERROR: This script must be run as Administrator" "Red"
        $allGood = $false
    }
    else {
        Write-ColorOutput "✓ Running as Administrator" "Green"
    }
    
    # Check prerequisites based on renewal type
    if ($Type -eq "Public" -or $Type -eq "Both") {
        # Check OpenSSL
        try {
            $opensslVersion = & openssl version 2>$null
            Write-ColorOutput "✓ OpenSSL is installed: $opensslVersion" "Green"
        }
        catch {
            Write-ColorOutput "✗ ERROR: OpenSSL is not installed or not in PATH" "Red"
            Write-ColorOutput "  Download from: https://slproweb.com/products/Win32OpenSSL.html" "Yellow"
            $allGood = $false
        }
        
        # Check private key
        if (Test-Path $PrivateKeyPath) {
            Write-ColorOutput "✓ Private key found: $PrivateKeyPath" "Green"
        }
        else {
            Write-ColorOutput "✗ ERROR: Private key not found at $PrivateKeyPath" "Red"
            Write-ColorOutput "  Please check IT Glue for backup key location" "Yellow"
            $allGood = $false
        }
        
        # Check certificate files
        $requiredFiles = @(
            "cvfnotaires.ca-certificate.crt",
            "cvfnotaires.ca-intermediate.pem",
            "cvfnotaires.ca-root.pem"
        )
        
        Write-ColorOutput "Checking certificate files in: $CertificatesPath" "White"
        foreach ($fileName in $requiredFiles) {
            $filePath = Join-Path $CertificatesPath $fileName
            if (Test-Path $filePath) {
                Write-ColorOutput "✓ Found: $fileName" "Green"
            }
            else {
                Write-ColorOutput "✗ ERROR: Missing certificate file: $fileName" "Red"
                $allGood = $false
            }
        }
    }
    
    if ($Type -eq "SelfSigned" -or $Type -eq "Both") {
        # Check PowerShell certificate cmdlets
        try {
            Get-Command New-SelfSignedCertificate -ErrorAction Stop | Out-Null
            Write-ColorOutput "✓ PowerShell certificate cmdlets available" "Green"
        }
        catch {
            Write-ColorOutput "✗ ERROR: PowerShell certificate cmdlets not available" "Red"
            $allGood = $false
        }
    }
    
    if (-not $allGood) {
        Write-Host ""
        Write-ColorOutput "Prerequisites check failed. Please fix the errors above and try again." "Red"
        exit 1
    }
    
    Write-ColorOutput "All prerequisites met for $Type renewal!" "Green"
    Write-Host ""
}

# Function to perform cleanup
function Start-Cleanup {
    param(
        [string]$Type
    )
    
    Write-ColorOutput "=== Performing Cleanup for $Type renewal ===" "Cyan"
    
    try {
        # Create backup directory for old PFX
        $backupDate = Get-Date -Format "yyyy-MM-dd-HHmm"
        $backupPath = Join-Path $SSLPath "backup-$backupDate"
        
        # Backup old PFX if it exists and we are renewing public cert
        if (($Type -eq "Public" -or $Type -eq "Both") -and (Test-Path $OutputPFXPath)) {
            New-Item -ItemType Directory -Path $backupPath -Force | Out-Null
            Move-Item $OutputPFXPath (Join-Path $backupPath "cvfnotaires-old.pfx")
            Write-ColorOutput "✓ Backed up old PFX to: $backupPath" "Green"
        }
        
        # Clean old backup folders (keep last 3 years)
        $oldBackups = Get-ChildItem -Path $SSLPath -Directory | Where-Object { 
            $_.Name -like "backup-*" -and $_.CreationTime -lt (Get-Date).AddYears(-3) 
        }
        
        foreach ($backup in $oldBackups) {
            Remove-Item $backup.FullName -Recurse -Force
            Write-ColorOutput "✓ Removed old backup: $($backup.Name)" "Yellow"
        }
        
        # Clean old certificates from Windows store based on type
        Write-ColorOutput "Cleaning expired certificates from Windows store..." "White"
        
        if ($Type -eq "Public" -or $Type -eq "Both") {
            # Clean cvfnotaires.ca certificates (keep current + 1 previous)
            $publicCerts = Get-ChildItem -Path "Cert:\LocalMachine\My" | Where-Object { 
                $_.Subject -like "*cvfnotaires.ca*" -and 
                $_.NotAfter -lt (Get-Date).AddDays(-30)
            } | Sort-Object NotAfter -Descending | Select-Object -Skip 1
            
            foreach ($cert in $publicCerts) {
                Remove-Item -Path "Cert:\LocalMachine\My\$($cert.Thumbprint)" -Force
                Write-ColorOutput "✓ Removed expired certificate: cvfnotaires.ca (Expires: $($cert.NotAfter))" "Yellow"
            }
        }
        
        if ($Type -eq "SelfSigned" -or $Type -eq "Both") {
            # Clean cvfnotaires.local certificates (keep current + 1 previous)
            $localCerts = Get-ChildItem -Path "Cert:\LocalMachine\My" | Where-Object { 
                $_.Subject -like "*cvfnotaires.local*" -and 
                $_.NotAfter -lt (Get-Date).AddDays(-30)
            } | Sort-Object NotAfter -Descending | Select-Object -Skip 1
            
            foreach ($cert in $localCerts) {
                Remove-Item -Path "Cert:\LocalMachine\My\$($cert.Thumbprint)" -Force
                Write-ColorOutput "✓ Removed old self-signed certificate: cvfnotaires.local" "Yellow"
            }
        }
        
        Write-ColorOutput "Cleanup completed!" "Green"
    }
    catch {
        Write-ColorOutput "WARNING: Cleanup error: $_" "Yellow"
        Write-ColorOutput "Continuing with renewal..." "Yellow"
    }
    
    Write-Host ""
}

# Function to create PFX certificate
function New-PFXCertificate {
    param(
        [SecureString]$Password
    )

    Write-ColorOutput "=== Creating PFX Certificate ===" "Cyan"

    $friendlyName = "cvfnotaires.ca SSL Certificate $Year"

    # Convert SecureString to plain text for OpenSSL
    $passwordText = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($Password))

    # Build OpenSSL command
    $certFile = Join-Path $CertificatesPath "cvfnotaires.ca-certificate.crt"
    $intermediateFile = Join-Path $CertificatesPath "cvfnotaires.ca-intermediate.pem"
    $rootFile = Join-Path $CertificatesPath "cvfnotaires.ca-root.pem"

    $opensslCmd = "pkcs12 -export -out '$OutputPFXPath' -inkey '$PrivateKeyPath' -in '$certFile' -certfile '$intermediateFile' -certfile '$rootFile' -name '$friendlyName' -passout pass:$passwordText"

    Write-ColorOutput "Creating PFX with friendly name: $friendlyName" "White"

    try {
        $result = & openssl $opensslCmd.Split(' ') 2>&1

        if ($LASTEXITCODE -eq 0) {
            Write-ColorOutput "✓ PFX created successfully: $OutputPFXPath" "Green"
            return $friendlyName
        }
        else {
            throw "OpenSSL error: $result"
        }
    }
    catch {
        Write-ColorOutput "ERROR: Failed to create PFX: $_" "Red"
        exit 1
    }

    Write-Host ""
}

# Function to import certificate to Windows store
function Import-Certificate {
    param(
        [SecureString]$Password
    )

    Write-ColorOutput "=== Importing Public Certificate to Windows Store ===" "Cyan"

    try {
        $cert = Import-PfxCertificate -FilePath $OutputPFXPath -CertStoreLocation "Cert:\LocalMachine\My" -Password $Password

        Write-ColorOutput "✓ Certificate imported successfully" "Green"
        Write-ColorOutput "  Thumbprint: $($cert.Thumbprint)" "Gray"
        Write-ColorOutput "  Subject: $($cert.Subject)" "Gray"
        Write-ColorOutput "  Expires: $($cert.NotAfter)" "Gray"

        return $cert
    }
    catch {
        Write-ColorOutput "ERROR: Failed to import certificate: $_" "Red"
        exit 1
    }

    Write-Host ""
}

# Function to get or create self-signed certificate for RDSH
function Get-RDSHCertificate {
    param(
        [bool]$ForceRenew = $false
    )

    Write-ColorOutput "=== Managing RDSH Certificate (cvfnotaires.local) ===" "Cyan"

    # Check for existing valid certificate
    $existingCert = Get-ChildItem -Path "Cert:\LocalMachine\My" | Where-Object {
        $_.Subject -like "*cvfnotaires.local*" -and
        $_.NotAfter -gt (Get-Date).AddDays(30)
    } | Sort-Object NotAfter -Descending | Select-Object -First 1

    if ($existingCert -and -not $ForceRenew) {
        Write-ColorOutput "✓ Valid self-signed certificate found" "Green"
        Write-ColorOutput "  Expires: $($existingCert.NotAfter)" "Gray"
        Write-ColorOutput "  Thumbprint: $($existingCert.Thumbprint)" "Gray"
        return $existingCert
    }

    # Create new self-signed certificate
    Write-ColorOutput "Creating new self-signed certificate for cvfnotaires.local..." "Yellow"

    try {
        $newCert = New-SelfSignedCertificate -DnsName "cvfnotaires.local" -CertStoreLocation "Cert:\LocalMachine\My" -KeyLength 2048 -KeyAlgorithm RSA -KeyUsage DigitalSignature, KeyEncipherment -Type SSLServerAuthentication -NotAfter (Get-Date).AddYears(2) -FriendlyName "cvfnotaires.local RDSH Certificate $Year"

        Write-ColorOutput "✓ Self-signed certificate created" "Green"
        Write-ColorOutput "  Expires: $($newCert.NotAfter)" "Gray"
        Write-ColorOutput "  Thumbprint: $($newCert.Thumbprint)" "Gray"

        return $newCert
    }
    catch {
        Write-ColorOutput "ERROR: Failed to create self-signed certificate: $_" "Red"
        exit 1
    }

    Write-Host ""
}

# Function to update IIS bindings
function Update-IISBindings {
    param(
        [System.Security.Cryptography.X509Certificates.X509Certificate2]$Certificate
    )

    Write-ColorOutput "=== Updating IIS SSL Bindings ===" "Cyan"

    try {
        Import-Module WebAdministration -ErrorAction Stop

        # Get all HTTPS bindings
        $bindings = Get-WebBinding | Where-Object { $_.protocol -eq "https" }

        if ($bindings.Count -eq 0) {
            Write-ColorOutput "No HTTPS bindings found in IIS" "Yellow"
            return
        }

        foreach ($binding in $bindings) {
            $siteName = $binding.ItemXPath -replace '.*\[@name=''([^'']+)''\].*', '$1'
            $bindingInfo = $binding.bindingInformation
            $port = $bindingInfo.Split(':')[1]

            Write-ColorOutput "Updating binding for $siteName (port $port)..." "White"

            try {
                # Update the SSL certificate for this binding
                $binding.AddSslCertificate($Certificate.Thumbprint, "my")
                Write-ColorOutput "✓ Updated: $siteName" "Green"
            }
            catch {
                # Try alternative method
                Remove-WebBinding -Name $siteName -Protocol "https" -Port $port
                New-WebBinding -Name $siteName -Protocol "https" -Port $port
                $newBinding = Get-WebBinding -Name $siteName -Protocol "https" -Port $port
                $newBinding.AddSslCertificate($Certificate.Thumbprint, "my")
                Write-ColorOutput "✓ Updated: $siteName (recreated binding)" "Green"
            }
        }

        Write-ColorOutput "IIS bindings updated successfully!" "Green"
    }
    catch {
        Write-ColorOutput "WARNING: Could not update IIS bindings automatically" "Yellow"
        Write-ColorOutput "Please update manually in IIS Manager" "Yellow"
        Write-ColorOutput "Error: $_" "Gray"
    }

    Write-Host ""
}

# Function to update RDS certificates
function Update-RDSCertificates {
    param(
        [System.Security.Cryptography.X509Certificates.X509Certificate2]$PublicCert,
        [System.Security.Cryptography.X509Certificates.X509Certificate2]$LocalCert,
        [string]$RenewalType
    )

    Write-ColorOutput "=== Updating RDS Certificates ===" "Cyan"

    # Update RD Session Host (uses self-signed for cvfnotaires.local)
    if (($RenewalType -eq "SelfSigned" -or $RenewalType -eq "Both") -and $LocalCert) {
        try {
            Write-ColorOutput "Updating RD Session Host certificate..." "White"
            $result = wmic /namespace:\\root\cimv2\TerminalServices PATH Win32_TSGeneralSetting Set SSLCertificateSHA1Hash="$($LocalCert.Thumbprint)" 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "✓ RD Session Host updated (cvfnotaires.local)" "Green"
            }
            else {
                Write-ColorOutput "WARNING: Could not update RD Session Host certificate" "Yellow"
                Write-ColorOutput "Error details: $result" "Gray"
            }
        }
        catch {
            Write-ColorOutput "WARNING: Error updating RD Session Host: $_" "Yellow"
        }
    }

    # Check and update RD Gateway (uses public certificate)
    if (($RenewalType -eq "Public" -or $RenewalType -eq "Both") -and $PublicCert) {
        $rdGateway = Get-WindowsFeature -Name RDS-Gateway -ErrorAction SilentlyContinue
        if ($rdGateway -and $rdGateway.InstallState -eq "Installed") {
            Write-ColorOutput "Updating RD Gateway certificate..." "White"
            Write-ColorOutput "  Please update manually in Server Manager" "Yellow"
            Write-ColorOutput "  Use certificate: cvfnotaires.ca SSL Certificate $Year" "Yellow"
        }

        # Check and update RD Web Access (uses public certificate)
        $rdWebAccess = Get-WindowsFeature -Name RDS-Web-Access -ErrorAction SilentlyContinue
        if ($rdWebAccess -and $rdWebAccess.InstallState -eq "Installed") {
            Write-ColorOutput "Updating RD Web Access certificate..." "White"
            Write-ColorOutput "  Please update manually in Server Manager" "Yellow"
            Write-ColorOutput "  Use certificate: cvfnotaires.ca SSL Certificate $Year" "Yellow"
        }
    }

    Write-Host ""
}

# Function to restart services
function Restart-Services {
    param(
        [string]$RenewalType
    )

    Write-ColorOutput "=== Restarting Services ===" "Cyan"

    # Restart IIS if public certificate was renewed
    if ($RenewalType -eq "Public" -or $RenewalType -eq "Both") {
        Write-ColorOutput "Restarting IIS..." "White"
        try {
            iisreset /noforce
            Write-ColorOutput "✓ IIS restarted" "Green"
        }
        catch {
            Write-ColorOutput "WARNING: Failed to restart IIS" "Yellow"
        }
    }

    # Restart Terminal Services if self-signed certificate was renewed
    if ($RenewalType -eq "SelfSigned" -or $RenewalType -eq "Both") {
        Write-ColorOutput "Restarting Terminal Services..." "White"
        try {
            Restart-Service -Name "TermService" -Force
            Write-ColorOutput "✓ Terminal Services restarted" "Green"
        }
        catch {
            Write-ColorOutput "WARNING: Failed to restart Terminal Services" "Yellow"
        }
    }

    # Restart RD Gateway if installed and public cert was renewed
    if (($RenewalType -eq "Public" -or $RenewalType -eq "Both") -and (Get-Service -Name "TSGateway" -ErrorAction SilentlyContinue)) {
        Write-ColorOutput "Restarting RD Gateway..." "White"
        try {
            Restart-Service -Name "TSGateway" -Force
            Write-ColorOutput "✓ RD Gateway restarted" "Green"
        }
        catch {
            Write-ColorOutput "WARNING: Failed to restart RD Gateway" "Yellow"
        }
    }

    Write-Host ""
}

# Function to test configuration
function Test-Configuration {
    param(
        [System.Security.Cryptography.X509Certificates.X509Certificate2]$PublicCert,
        [System.Security.Cryptography.X509Certificates.X509Certificate2]$LocalCert,
        [string]$RenewalType
    )

    Write-ColorOutput "=== Running Verification Tests ===" "Cyan"

    # Test certificates in store
    Write-ColorOutput "Checking certificates in Windows store..." "White"

    if (($RenewalType -eq "Public" -or $RenewalType -eq "Both") -and $PublicCert) {
        Write-ColorOutput "✓ Public certificate (cvfnotaires.ca)" "Green"
        Write-ColorOutput "  Expires: $($PublicCert.NotAfter)" "Gray"
    }

    if (($RenewalType -eq "SelfSigned" -or $RenewalType -eq "Both") -and $LocalCert) {
        Write-ColorOutput "✓ RDSH certificate (cvfnotaires.local)" "Green"
        Write-ColorOutput "  Expires: $($LocalCert.NotAfter)" "Gray"
    }

    # Test IIS if public cert was renewed
    if ($RenewalType -eq "Public" -or $RenewalType -eq "Both") {
        try {
            $site = Get-IISSite | Select-Object -First 1
            if ($site) {
                Write-ColorOutput "✓ IIS is responding" "Green"
            }
        }
        catch {
            Write-ColorOutput "WARNING: Could not verify IIS" "Yellow"
        }
    }

    # Test RDP if self-signed cert was renewed
    if ($RenewalType -eq "SelfSigned" -or $RenewalType -eq "Both") {
        $rdpPort = Get-NetTCPConnection -LocalPort 3389 -State Listen -ErrorAction SilentlyContinue
        if ($rdpPort) {
            Write-ColorOutput "✓ RDP port 3389 is listening" "Green"
        }
        else {
            Write-ColorOutput "WARNING: RDP port 3389 not listening" "Yellow"
        }
    }

    Write-Host ""
}

# Function to show completion summary
function Show-Summary {
    param(
        [System.Security.Cryptography.X509Certificates.X509Certificate2]$PublicCert,
        [System.Security.Cryptography.X509Certificates.X509Certificate2]$LocalCert,
        [string]$FriendlyName,
        [string]$RenewalType
    )

    Write-ColorOutput "========================================" "Green"
    Write-ColorOutput "    Certificate Renewal Completed!      " "Green"
    Write-ColorOutput "          ($RenewalType)                " "Green"
    Write-ColorOutput "========================================" "Green"
    Write-Host ""

    if (($RenewalType -eq "Public" -or $RenewalType -eq "Both") -and $PublicCert) {
        Write-ColorOutput "Public Certificate (cvfnotaires.ca):" "White"
        Write-ColorOutput "  Friendly Name: $FriendlyName" "Gray"
        Write-ColorOutput "  Thumbprint: $($PublicCert.Thumbprint)" "Gray"
        Write-ColorOutput "  Expires: $($PublicCert.NotAfter)" "Gray"
        Write-ColorOutput "  Used by: IIS, RD Gateway, RD Web Access" "Gray"
        Write-Host ""
    }

    if (($RenewalType -eq "SelfSigned" -or $RenewalType -eq "Both") -and $LocalCert) {
        Write-ColorOutput "RDSH Certificate (cvfnotaires.local):" "White"
        Write-ColorOutput "  Thumbprint: $($LocalCert.Thumbprint)" "Gray"
        Write-ColorOutput "  Expires: $($LocalCert.NotAfter)" "Gray"
        Write-ColorOutput "  Used by: RD Session Host" "Gray"
        Write-Host ""
    }

    Write-ColorOutput "Next Steps:" "Yellow"

    if ($RenewalType -eq "Public" -or $RenewalType -eq "Both") {
        Write-ColorOutput "1. Test HTTPS access to websites" "White"
        Write-ColorOutput "3. Update RD Gateway certificate in Server Manager (if needed)" "White"
        Write-ColorOutput "4. Update RD Web Access certificate in Server Manager (if needed)" "White"
        Write-ColorOutput "5. Document PFX password in IT Glue" "White"
    }

    if ($RenewalType -eq "SelfSigned" -or $RenewalType -eq "Both") {
        Write-ColorOutput "2. Test RDP connections" "White"
    }

    Write-ColorOutput "6. Update certificate expiration dates in documentation" "White"
    Write-Host ""

    if ($RenewalType -eq "Public" -or $RenewalType -eq "Both") {
        Write-ColorOutput "IMPORTANT: Remember to securely document the PFX password!" "Red"
    }
    Write-Host ""
}

# Main execution
function Main {
    Show-Banner

    # Get renewal type
    $selectedRenewalType = Get-RenewalType
    Write-ColorOutput "Selected renewal type: $selectedRenewalType" "Cyan"
    Write-Host ""

    # Run prerequisite checks
    Test-Prerequisites -Type $selectedRenewalType
    Start-Cleanup -Type $selectedRenewalType

    # Initialize variables
    $publicCert = $null
    $localCert = $null
    $friendlyName = ""

    # Handle public certificate renewal
    if ($selectedRenewalType -eq "Public" -or $selectedRenewalType -eq "Both") {
        # Get password for PFX
        Write-ColorOutput "=== Public Certificate Password ===" "Cyan"
        $password = Read-Host "Enter password for new PFX file" -AsSecureString
        Write-Host ""

        # Create and import public certificate
        $friendlyName = New-PFXCertificate -Password $password
        $publicCert = Import-Certificate -Password $password

        # Update IIS bindings
        Update-IISBindings -Certificate $publicCert
    }

    # Handle self-signed certificate renewal
    if ($selectedRenewalType -eq "SelfSigned" -or $selectedRenewalType -eq "Both") {
        $forceRenew = ($selectedRenewalType -eq "SelfSigned")
        $localCert = Get-RDSHCertificate -ForceRenew $forceRenew
    }

    # Update RDS configurations
    Update-RDSCertificates -PublicCert $publicCert -LocalCert $localCert -RenewalType $selectedRenewalType

    # Restart services
    Restart-Services -RenewalType $selectedRenewalType

    # Test configuration
    Test-Configuration -PublicCert $publicCert -LocalCert $localCert -RenewalType $selectedRenewalType

    # Show summary
    Show-Summary -PublicCert $publicCert -LocalCert $localCert -FriendlyName $friendlyName -RenewalType $selectedRenewalType
}

# Run the script
Main
