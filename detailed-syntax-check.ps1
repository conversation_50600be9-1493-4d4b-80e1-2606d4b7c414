# Detailed PowerShell Syntax Validation Script
param(
    [string]$FilePath = "Complete-SSL-Renewal.ps1"
)

function Test-BraceMatching {
    param([string]$Content)
    
    $lines = $Content -split "`r?`n"
    $braceStack = @()
    $issues = @()
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        $lineNum = $i + 1
        
        # Count braces in this line
        $openBraces = ($line.ToCharArray() | Where-Object { $_ -eq '{' }).Count
        $closeBraces = ($line.ToCharArray() | Where-Object { $_ -eq '}' }).Count
        
        # Add open braces to stack
        for ($j = 0; $j -lt $openBraces; $j++) {
            $braceStack += $lineNum
        }
        
        # Remove close braces from stack
        for ($j = 0; $j -lt $closeBraces; $j++) {
            if ($braceStack.Count -eq 0) {
                $issues += "Line $lineNum`: Unexpected closing brace '}'"
            } else {
                $braceStack = $braceStack[0..($braceStack.Count-2)]
            }
        }
    }
    
    # Check for unmatched opening braces
    foreach ($openLine in $braceStack) {
        $issues += "Line $openLine`: Unmatched opening brace '{'"
    }
    
    return $issues
}

function Test-TryCatchBlocks {
    param([string]$Content)
    
    $issues = @()
    $lines = $Content -split "`r?`n"
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i].Trim()
        $lineNum = $i + 1
        
        if ($line -match '^\s*try\s*\{') {
            # Found a try block, look for matching catch or finally
            $braceCount = 1
            $foundCatchOrFinally = $false
            
            for ($j = $i + 1; $j -lt $lines.Count -and $braceCount -gt 0; $j++) {
                $nextLine = $lines[$j]
                
                # Count braces
                $openBraces = ($nextLine.ToCharArray() | Where-Object { $_ -eq '{' }).Count
                $closeBraces = ($nextLine.ToCharArray() | Where-Object { $_ -eq '}' }).Count
                $braceCount += $openBraces - $closeBraces
                
                # Check for catch or finally
                if ($nextLine.Trim() -match '^\s*(catch|finally)\s*\{?') {
                    $foundCatchOrFinally = $true
                }
                
                # If we've closed the try block
                if ($braceCount -eq 0) {
                    break
                }
            }
            
            if (-not $foundCatchOrFinally) {
                $issues += "Line $lineNum`: Try block missing catch or finally block"
            }
        }
    }
    
    return $issues
}

function Test-StringTerminators {
    param([string]$Content)
    
    $issues = @()
    $lines = $Content -split "`r?`n"
    
    for ($i = 0; $i -lt $lines.Count; $i++) {
        $line = $lines[$i]
        $lineNum = $i + 1
        
        # Check for unterminated double quotes
        $doubleQuotes = ($line.ToCharArray() | Where-Object { $_ -eq '"' }).Count
        if ($doubleQuotes % 2 -ne 0) {
            $issues += "Line $lineNum`: Possible unterminated string (odd number of double quotes)"
        }
        
        # Check for unterminated single quotes
        $singleQuotes = ($line.ToCharArray() | Where-Object { $_ -eq "'" }).Count
        if ($singleQuotes % 2 -ne 0) {
            $issues += "Line $lineNum`: Possible unterminated string (odd number of single quotes)"
        }
    }
    
    return $issues
}

try {
    if (-not (Test-Path $FilePath)) {
        Write-Host "File not found: $FilePath" -ForegroundColor Red
        exit 1
    }
    
    $content = Get-Content $FilePath -Raw
    
    Write-Host "=== PowerShell Syntax Analysis ===" -ForegroundColor Cyan
    Write-Host "File: $FilePath" -ForegroundColor Gray
    Write-Host ""
    
    # Test brace matching
    Write-Host "Checking brace matching..." -ForegroundColor Yellow
    $braceIssues = Test-BraceMatching -Content $content
    if ($braceIssues.Count -gt 0) {
        Write-Host "Brace Issues Found:" -ForegroundColor Red
        $braceIssues | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
    } else {
        Write-Host "  ✓ No brace matching issues" -ForegroundColor Green
    }
    Write-Host ""
    
    # Test try-catch blocks
    Write-Host "Checking try-catch blocks..." -ForegroundColor Yellow
    $tryCatchIssues = Test-TryCatchBlocks -Content $content
    if ($tryCatchIssues.Count -gt 0) {
        Write-Host "Try-Catch Issues Found:" -ForegroundColor Red
        $tryCatchIssues | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
    } else {
        Write-Host "  ✓ No try-catch block issues" -ForegroundColor Green
    }
    Write-Host ""
    
    # Test string terminators
    Write-Host "Checking string terminators..." -ForegroundColor Yellow
    $stringIssues = Test-StringTerminators -Content $content
    if ($stringIssues.Count -gt 0) {
        Write-Host "String Terminator Issues Found:" -ForegroundColor Red
        $stringIssues | ForEach-Object { Write-Host "  $_" -ForegroundColor Yellow }
    } else {
        Write-Host "  ✓ No string terminator issues" -ForegroundColor Green
    }
    Write-Host ""
    
    # Try PowerShell parser
    Write-Host "Running PowerShell parser..." -ForegroundColor Yellow
    $errors = $null
    $tokens = $null
    $ast = [System.Management.Automation.Language.Parser]::ParseFile($FilePath, [ref]$tokens, [ref]$errors)
    
    if ($errors.Count -gt 0) {
        Write-Host "PowerShell Parser Errors:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  Line $($error.Extent.StartLineNumber): $($error.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ✓ PowerShell parser found no errors" -ForegroundColor Green
    }
    
    $totalIssues = $braceIssues.Count + $tryCatchIssues.Count + $stringIssues.Count + $errors.Count
    
    Write-Host ""
    if ($totalIssues -eq 0) {
        Write-Host "=== ANALYSIS COMPLETE: No issues found ===" -ForegroundColor Green
        exit 0
    } else {
        Write-Host "=== ANALYSIS COMPLETE: $totalIssues issues found ===" -ForegroundColor Red
        exit 1
    }
    
} catch {
    Write-Host "Error during analysis: $_" -ForegroundColor Red
    exit 1
}
