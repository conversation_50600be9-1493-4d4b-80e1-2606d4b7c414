# PowerShell Syntax Validation Script
param(
    [string]$FilePath = "Complete-SSL-Renewal.ps1"
)

try {
    # Try to parse the PowerShell script
    $errors = $null
    $tokens = $null
    $null = [System.Management.Automation.Language.Parser]::ParseFile($FilePath, [ref]$tokens, [ref]$errors)
    
    if ($errors.Count -gt 0) {
        Write-Host "Syntax Errors Found:" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "Line $($error.Extent.StartLineNumber): $($error.Message)" -ForegroundColor Yellow
        }
        exit 1
    } else {
        Write-Host "No syntax errors found!" -ForegroundColor Green
        exit 0
    }
} catch {
    Write-Host "Error parsing file: $_" -ForegroundColor Red
    exit 1
}
